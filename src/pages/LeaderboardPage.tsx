import { useState, useMemo, useEffect } from "react"; // Added useEffect
import { useTranslation } from "react-i18next";
import UnifiedLayout from "@/components/layout/UnifiedLayout";
import { Medal, ArrowUpDown, Search, Trophy } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CompactMobileTable } from "@/components/ui/compact-mobile-table";
import { MobileTable } from "@/components/ui/mobile-table";
import { useMediaQuery } from "@/hooks/use-media-query";
import { parseISO } from "date-fns"; // For loading matches
import { supabase } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import { calculatePlayerRating } from "@/utils/playerRating";

// localStorage Keys
const PLAYERS_STORAGE_KEY = 'soccerPlayersData';
const MATCHES_STORAGE_KEY = 'soccerMatchesData';

// --- Data Loading & Interfaces ---

interface PlayerCore {
  id: number;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
}

interface Match {
  id: number;
  date: Date;
  teamA: number[];
  teamB: number[];
  scoreA: number | null;
  scoreB: number | null;
  winner?: 'A' | 'B' | 'Draw';
  goalscorers?: any[];
  youtubeLink?: string;
}

// Function to load data from localStorage
function loadData<T>(key: string, fallback: T[]): T[] {
    try {
      const storedData = typeof window !== 'undefined' ? localStorage.getItem(key) : null;
      if (storedData) {
          const parsedData = JSON.parse(storedData) as any[];
          if (key === MATCHES_STORAGE_KEY) {
              return parsedData.map(item => ({
                  ...item,
                  date: item.date ? parseISO(item.date) : new Date(0) // Handle date parsing
              })) as T[];
          }
          return parsedData as T[];
      }
    } catch (error) {
      console.error(`Error reading ${key} from localStorage:`, error);
    }
    return fallback;
}

// --- Helper Functions ---
const calculateAverageRating = (player: Omit<PlayerCore, 'id' | 'name'>): number => {
  return calculatePlayerRating(player);
};

// Define a type for a completed World Cup Run
interface CompletedWorldCupRun {
  date: Date; // Date when the World Cup was won (date of the last knockout match)
}

// Calculate World Cup Run count for a player
const calculateWorldCupRunCount = (matches: any[], playerId: number): number => {
  // Get matches where this player participated, sorted by date (newest first)
  const playerMatches = matches
    .filter(match => (match.teamA?.includes(playerId) || match.teamB?.includes(playerId)))
    .sort((a, b) => {
      const dateA = a.date instanceof Date ? a.date : new Date(a.date);
      const dateB = b.date instanceof Date ? b.date : new Date(b.date);
      return dateB.getTime() - dateA.getTime(); // Sort newest first
    });

  // If no matches, return 0
  if (playerMatches.length === 0) return 0;

  // Helper function to check if player won a match
  const didPlayerWin = (match: any): boolean => {
    const isInTeamA = match.teamA?.includes(playerId);
    return (isInTeamA && match.winner === 'A') || (!isInTeamA && match.winner === 'B');
  };

  // Helper function to check if match was a draw
  const isMatchDraw = (match: any): boolean => {
    return match.winner === 'Draw';
  };

  // Helper function to check if a group stage is valid (at least 2 wins, or 1 win and 1 draw)
  const isValidGroupStage = (matches: any[]): boolean => {
    if (matches.length !== 3) return false;

    let wins = 0;
    let draws = 0;

    matches.forEach(match => {
      if (didPlayerWin(match)) wins++;
      else if (isMatchDraw(match)) draws++;
    });

    // Valid if: 2+ wins OR 1 win + 1+ draws
    return wins >= 2 || (wins === 1 && draws >= 1);
  };

  // Track World Cup run count
  let worldCupRunCount = 0;

  // Create a chronologically ordered copy of the matches (oldest first)
  const chronologicalMatches = [...playerMatches].reverse();

  // Scan for completed World Cup runs
  let i = 0;
  while (i <= chronologicalMatches.length - 7) { // Need at least 7 matches for a complete run
    // Check if we have a valid group stage (3 consecutive matches)
    const potentialGroupStage = chronologicalMatches.slice(i, i + 3);

    if (isValidGroupStage(potentialGroupStage)) {
      // Found a valid group stage, now check for knockout matches
      const knockoutMatches = chronologicalMatches.slice(i + 3, i + 7);

      // Check if all knockout matches were wins
      const allKnockoutWins = knockoutMatches.every(match => didPlayerWin(match));

      if (allKnockoutWins) {
        // Found a valid World Cup run!
        worldCupRunCount++;

        // Skip this entire run
        i += 7;
        continue;
      }
    }

    // Move to the next potential starting point
    i++;
  }

  return worldCupRunCount;
};

// --- Component ---

// Combined type for Leaderboard
interface LeaderboardPlayer extends PlayerCore {
  played: number;
  wins: number;
  draws: number;
  losses: number;
  winRate: number;
  rating: number;
  worldCupRuns: number; // Number of World Cup Runs won
}

type SortField = "name" | "played" | "winRate" | "rating" | "wins" | "draws" | "losses" | "worldCupRuns"; // Added worldCupRuns to sortable fields

const LeaderboardPage = () => {
  const { t } = useTranslation();
  const [players, setPlayers] = useState<LeaderboardPlayer[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const isMobile = useMediaQuery("(max-width: 768px)");

  const calculateLeaderboardData = (players: any[], matches: any[], year: string = 'all'): LeaderboardPlayer[] => {
    // Filter matches by year if a specific year is selected
    const filteredMatches = year === 'all'
      ? matches
      : matches.filter(match => {
          const matchDate = new Date(match.match_date);
          return matchDate.getFullYear() === parseInt(year);
        });

    return players.map(player => {
      const playerMatches = filteredMatches.filter(
        match => match.teama.includes(player.id) || match.teamb.includes(player.id)
      );

      const gamesPlayed = playerMatches.length;
      const wins = playerMatches.filter(match => {
        const inTeamA = match.teama.includes(player.id);
        return (inTeamA && match.winner === 'A') || (!inTeamA && match.winner === 'B');
      }).length;
      const draws = playerMatches.filter(match => match.winner === 'Draw').length;
      const losses = gamesPlayed - wins - draws;

      // Calculate World Cup Runs won
      const worldCupRuns = calculateWorldCupRunCount(filteredMatches, player.id);

      return {
        id: player.id,
        name: player.name,
        skills: player.skills,
        effort: player.effort,
        stamina: player.stamina,
        played: gamesPlayed,
        wins,
        draws,
        losses,
        winRate: gamesPlayed > 0 ? (wins / gamesPlayed) * 100 : 0,
        rating: Math.round((player.skills + player.effort + player.stamina) / 3),
        worldCupRuns
      };
    });
  };

  // Get the selected group ID from localStorage
  const selectedGroupId = localStorage.getItem('selectedGroupId');

  useEffect(() => {
    const fetchLeaderboardData = async () => {
      setLoading(true);

      // Check if a group is selected
      if (!selectedGroupId) {
        console.warn("No group selected. Please select a group first.");
        setPlayers([]);
        setLoading(false);
        return;
      }

      try {
        const [{ data: playersData }, { data: matchesData }] = await Promise.all([
          supabase.from('players').select('*').eq('group_id', selectedGroupId),
          supabase.from('matches').select('*').eq('group_id', selectedGroupId)
        ]);

        if (!playersData) throw new Error('No players found');

        // Transform match data to match our interface
        const transformedMatches = (matchesData || []).map(match => ({
          ...match,
          date: new Date(match.match_date),
          teamA: match.teama,
          teamB: match.teamb,
          scoreA: match.scorea,
          scoreB: match.scoreb,
        }));

        const leaderboardData = calculateLeaderboardData(playersData, transformedMatches, yearFilter);
        setPlayers(leaderboardData);
      } catch (error) {
        console.error("Error fetching leaderboard data:", error);
        toast({
          title: t('leaderboard.loadError', 'Error loading leaderboard'),
          description: error.message,
          variant: "destructive"
        });
        setPlayers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboardData();
  }, [toast, selectedGroupId, yearFilter]);

  const [search, setSearch] = useState("");
  const [sortField, setSortField] = useState<SortField>("winRate"); // Changed default to winRate
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [minGames, setMinGames] = useState<string>("10"); // Changed default to 10 games
  const [yearFilter, setYearFilter] = useState<string>("all"); // Add year filter

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      // Default sort directions
      setSortDirection(field === "name" ? "asc" : "desc");
    }
  };

  // Filter and sort the calculated leaderboard data
  const filteredAndSortedPlayers = useMemo(() => {
    const minGamesNum = parseInt(minGames);
    return [...players] // Use players state instead of leaderboardData
      .filter(
        (player) =>
          player.name.toLowerCase().includes(search.toLowerCase()) &&
          player.played >= minGamesNum
      )
      .sort((a, b) => {
        let comparison = 0;
        if (sortField === "name") {
          comparison = a.name.localeCompare(b.name);
        } else {
          comparison = (a[sortField] ?? 0) - (b[sortField] ?? 0);
        }
        return sortDirection === "asc" ? comparison : -comparison;
      });
  }, [players, search, minGames, sortField, sortDirection]);

  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return (
      <span className="inline-block ml-1">
        {sortDirection === "asc" ? "↑" : "↓"}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">{t('nav.leaderboard')}</h1>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-soccer-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold">{t('nav.leaderboard')}</h1>
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative w-full sm:w-80">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('players.searchPlayers')}
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-8"
            />
          </div>
          <div className="flex flex-wrap gap-2 items-center">
            <span className="text-sm whitespace-nowrap">{t('leaderboard.minGames', 'Min. games:')}:</span>
            <Select
              value={minGames}
              onValueChange={setMinGames}
            >
              <SelectTrigger className="w-[100px]">
                <SelectValue placeholder={t('leaderboard.minGamesPlaceholder', 'Min Games')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10+</SelectItem>  {/* Moved up */}
                <SelectItem value="15">15+</SelectItem>
                <SelectItem value="20">20+</SelectItem>
                <SelectItem value="5">5+</SelectItem>
                <SelectItem value="0">All</SelectItem>
              </SelectContent>
            </Select>

            <span className="text-sm whitespace-nowrap ml-2">{t('leaderboard.year', 'Year:')}:</span>
            <Select
              value={yearFilter}
              onValueChange={setYearFilter}
            >
              <SelectTrigger className="w-[100px]">
                <SelectValue placeholder={t('leaderboard.yearPlaceholder', 'Year')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('leaderboard.allYears', 'All Years')}</SelectItem>
                {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
                  <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {isMobile ? (
          <MobileTable
            data={filteredAndSortedPlayers}
            columns={[
              {
                header: t('leaderboard.rank', '#'),
                accessorKey: 'id' as keyof LeaderboardPlayer,
                priority: "high",
                cell: (player: LeaderboardPlayer) => {
                  const idx = filteredAndSortedPlayers.findIndex(p => p.id === player.id);
                  return (
                    <div className="flex items-center justify-center">
                      <div className="flex items-center justify-center w-5 h-5 rounded-full border-2 border-soccer-primary dark:border-soccer-primary bg-transparent">
                        <span className="text-soccer-primary dark:text-soccer-primary text-xs font-medium">{idx + 1}</span>
                      </div>
                    </div>
                  );
                },
                meta: { align: "center", width: "10%" }
              },
              {
                header: t('players.name'),
                accessorKey: 'name',
                priority: "high",
                cell: (player: LeaderboardPlayer) => (
                  <span className="text-xs font-medium truncate max-w-[60px] block">{player.name}</span>
                ),
                meta: { align: "left", width: "20%" }
              },
              {
                header: t('leaderboard.w', 'W'),
                accessorKey: 'wins',
                priority: "high",
                cell: (player: LeaderboardPlayer) => (
                  <span className="text-green-600 font-medium text-xs">{player.wins}</span>
                ),
                meta: { align: "center", width: "8%" }
              },
              {
                header: t('leaderboard.d', 'D'),
                accessorKey: 'draws',
                priority: "high",
                cell: (player: LeaderboardPlayer) => (
                  <span className="text-gray-500 font-medium text-xs">{player.draws}</span>
                ),
                meta: { align: "center", width: "8%" }
              },
              {
                header: t('leaderboard.l', 'L'),
                accessorKey: 'losses',
                priority: "high",
                cell: (player: LeaderboardPlayer) => (
                  <span className="text-red-600 font-medium text-xs">{player.losses}</span>
                ),
                meta: { align: "center", width: "8%" }
              },
              {
                header: t('leaderboard.winRate', 'Win%'),
                accessorKey: 'winRate',
                priority: "high",
                cell: (player: LeaderboardPlayer) => (
                  <span className={`text-xs font-medium ${player.winRate >= 70 ? "text-green-600" : player.winRate >= 50 ? "text-amber-600" : "text-red-600"}`}>
                    {player.winRate.toFixed(0)}%
                  </span>
                ),
                meta: { align: "center", width: "15%" }
              },
              {
                header: t('players.rating'),
                accessorKey: 'rating',
                priority: "high",
                cell: (player: LeaderboardPlayer) => (
                  <div className="flex items-center justify-center">
                    <div className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100 rounded-full px-1.5 py-0.5 text-xs font-medium">
                      {player.rating}
                    </div>
                  </div>
                ),
                meta: { align: "center", width: "15%" }
              },
              {
                header: t('leaderboard.wc', 'WC'),
                accessorKey: 'worldCupRuns',
                priority: "high",
                cell: (player: LeaderboardPlayer) => (
                  <div className="flex items-center justify-center">
                    {player.worldCupRuns > 0 ? (
                      <div className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100 rounded-full px-1.5 py-0.5 text-xs font-medium">
                        {player.worldCupRuns}x 🏆
                      </div>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </div>
                ),
                meta: { align: "center", width: "15%" }
              },
            ]}
            keyExtractor={(player) => player.id}
            emptyState={
              <div className="text-center py-8 text-muted-foreground">
                {t('leaderboard.noPlayersFound', 'No players found matching the criteria.')}
              </div>
            }
            isLoading={loading}
            rowClassName={(player) => ""}
          />
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px] text-center">{t('leaderboard.rank', 'Rank')}</TableHead>
                  <TableHead>
                    <Button variant="ghost" className="p-0 font-bold hover:bg-transparent" onClick={() => handleSort("name")}>
                      {t('players.name')} {renderSortIcon("name")}
                    </Button>
                  </TableHead>
                   {/* Make W/D/L headers sortable */}
                  <TableHead className="text-center">
                     <Button variant="ghost" className="p-0 font-bold hover:bg-transparent" onClick={() => handleSort("wins")}> {t('leaderboard.w', 'W')} {renderSortIcon("wins")}</Button>
                  </TableHead>
                  <TableHead className="text-center">
                      <Button variant="ghost" className="p-0 font-bold hover:bg-transparent" onClick={() => handleSort("draws")}> {t('leaderboard.d', 'D')} {renderSortIcon("draws")}</Button>
                  </TableHead>
                  <TableHead className="text-center">
                      <Button variant="ghost" className="p-0 font-bold hover:bg-transparent" onClick={() => handleSort("losses")}> {t('leaderboard.l', 'L')} {renderSortIcon("losses")}</Button>
                  </TableHead>
                  <TableHead className="text-right">
                    <Button variant="ghost" className="p-0 font-bold hover:bg-transparent" onClick={() => handleSort("winRate")}>
                      {t('players.winRate')} {renderSortIcon("winRate")}
                    </Button>
                  </TableHead>
                  <TableHead className="text-right">
                    <Button variant="ghost" className="p-0 font-bold hover:bg-transparent" onClick={() => handleSort("played")}>
                      {t('players.played')} {renderSortIcon("played")}
                    </Button>
                  </TableHead>
                  <TableHead className="text-right">
                    <Button variant="ghost" className="p-0 font-bold hover:bg-transparent" onClick={() => handleSort("rating")}>
                      {t('players.rating')} {renderSortIcon("rating")}
                    </Button>
                  </TableHead>
                  <TableHead className="text-center">
                    <Button variant="ghost" className="p-0 font-bold hover:bg-transparent" onClick={() => handleSort("worldCupRuns")}>
                      {t('leaderboard.wc', 'WC')} {renderSortIcon("worldCupRuns")}
                    </Button>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
               {filteredAndSortedPlayers.length === 0 ? (
                   <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                          {t('leaderboard.noPlayersFound', 'No players found matching the criteria.')}
                      </TableCell>
                   </TableRow>
               ) : (
                  filteredAndSortedPlayers.map((player, idx) => (
                    <TableRow key={player.id}>
                      <TableCell className="text-center">
                        <span>{idx + 1}</span>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{player.name}</div>
                      </TableCell>
                      <TableCell className="text-center text-green-600 font-medium">{player.wins}</TableCell>
                      <TableCell className="text-center text-gray-500 font-medium">{player.draws}</TableCell>
                      <TableCell className="text-center text-red-600 font-medium">{player.losses}</TableCell>
                      <TableCell className="text-right">
                        <span className={`${player.winRate >= 70 ? "text-green-600" : player.winRate >= 50 ? "text-amber-600" : "text-red-600"}`}>
                          {player.winRate.toFixed(1)}%
                        </span>
                      </TableCell>
                      <TableCell className="text-right">{player.played}</TableCell>
                      <TableCell className="text-right">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {player.rating}
                        </span>
                      </TableCell>
                      <TableCell className="text-center">
                        {player.worldCupRuns > 0 ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                            {player.worldCupRuns}x 🏆
                          </span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
               )}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
    </div>
  );
};

export default LeaderboardPage;
