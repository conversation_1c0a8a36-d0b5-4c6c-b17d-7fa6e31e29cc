
import { ReactNode } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface StatCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  trend?: "up" | "down" | "neutral";
  trendValue?: string;
  className?: string;
}

const StatCard = ({ title, value, icon, trend, trendValue, className }: StatCardProps) => {
  return (
    <Card
      hoverable
      className={cn("group", className)}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground group-hover:text-soccer-primary transition-colors duration-300">{title}</CardTitle>
        {icon && <div className="text-soccer-primary group-hover:scale-110 transition-transform duration-300">{icon}</div>}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold group-hover:text-soccer-primary transition-colors duration-300">{value}</div>
        {trend && trendValue && (
          <p className={cn(
            "text-xs mt-1 flex items-center gap-1 transition-colors duration-300",
            trend === "up" ? "text-green-600 dark:text-green-500" :
            trend === "down" ? "text-red-600 dark:text-red-500" :
            "text-muted-foreground"
          )}>
            <span className="group-hover:scale-125 transition-transform duration-300">{trend === "up" ? "↑" : trend === "down" ? "↓" : "→"}</span>
            {trendValue}
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default StatCard;
