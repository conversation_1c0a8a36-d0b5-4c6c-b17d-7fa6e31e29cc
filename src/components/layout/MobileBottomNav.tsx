import { Link } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  UserPlus,
  Trophy,
  UserCircle
} from "lucide-react";

const MobileBottomNav = () => {
  const location = useLocation();
  const { t } = useTranslation();

  // Navigation items
  const navigation = [
    { name: t('nav.dashboard'), href: "/dashboard", icon: LayoutDashboard },
    { name: t('nav.players'), href: "/players", icon: UserPlus },
    { name: t('nav.matches'), href: "/matches", icon: Trophy },
    { name: t('nav.profile'), href: "/profile", icon: UserCircle },
  ];

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 bg-soccer-primary dark:bg-soccer-primary-dark text-white z-50 border-t border-white/10 shadow-lg backdrop-blur-sm">
      <div className="grid grid-cols-4 h-16">
        {navigation.map((item) => (
          <Link
            key={item.name}
            to={item.href}
            className={cn(
              "flex flex-col items-center justify-center px-2 py-1 transition-all duration-300 relative group",
              location.pathname === item.href || (item.href === "/dashboard" && location.pathname.startsWith("/dashboard"))
                ? "bg-white/20 text-white shadow-md scale-105"
                : "text-white/80 hover:bg-white/10 hover:text-white hover:scale-105"
            )}
          >
            <item.icon className={cn(
              "h-5 w-5 mb-1 transition-all duration-300",
              location.pathname === item.href || (item.href === "/dashboard" && location.pathname.startsWith("/dashboard"))
                ? "scale-110"
                : "group-hover:scale-105"
            )} />
            <span className={cn(
              "text-xs font-medium transition-all duration-300",
              location.pathname === item.href || (item.href === "/dashboard" && location.pathname.startsWith("/dashboard"))
                ? "font-semibold"
                : ""
            )}>{item.name}</span>
            {(location.pathname === item.href || (item.href === "/dashboard" && location.pathname.startsWith("/dashboard"))) && (
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-10 h-1 bg-white rounded-full shadow-sm" />
            )}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default MobileBottomNav;
