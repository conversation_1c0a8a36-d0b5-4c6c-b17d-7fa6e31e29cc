import * as React from "react";
import { cn } from "@/lib/utils";
import { Card } from "@/components/ui/card";

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

function Skeleton({
  className,
  ...props
}: SkeletonProps) {
  return (
    <div
      className={cn("animate-pulse rounded-md bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%] animate-[shimmer_2s_infinite]", className)}
      {...props}
    />
  )
}

// Card skeleton with title, description and content
function CardSkeleton({ className, ...props }: SkeletonProps) {
  return (
    <Card className={cn("overflow-hidden", className)} {...props}>
      <div className="p-6">
        <Skeleton className="h-6 w-1/2 mb-2" />
        <Skeleton className="h-4 w-3/4 mb-4" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      </div>
      <div className="px-6 py-4 bg-muted/30 flex justify-end gap-2">
        <Skeleton className="h-9 w-20" />
        <Skeleton className="h-9 w-20" />
      </div>
    </Card>
  );
}

// Player card skeleton
function PlayerCardSkeleton({ className, ...props }: SkeletonProps) {
  return (
    <Card className={cn("overflow-hidden", className)} {...props}>
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-5 w-16 rounded-full" />
        </div>
        <Skeleton className="h-4 w-1/4 mb-4" />
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="space-y-1">
            <Skeleton className="h-3 w-12 mx-auto" />
            <Skeleton className="h-5 w-8 mx-auto" />
          </div>
          <div className="space-y-1">
            <Skeleton className="h-3 w-12 mx-auto" />
            <Skeleton className="h-5 w-8 mx-auto" />
          </div>
          <div className="space-y-1">
            <Skeleton className="h-3 w-12 mx-auto" />
            <Skeleton className="h-5 w-8 mx-auto" />
          </div>
        </div>
      </div>
      <div className="px-6 py-4 bg-muted/30 flex justify-between">
        <Skeleton className="h-9 w-24" />
        <Skeleton className="h-9 w-24" />
      </div>
    </Card>
  );
}

// Table row skeleton
function TableRowSkeleton({ className, ...props }: SkeletonProps) {
  return (
    <div className={cn("flex items-center space-x-4 py-3", className)} {...props}>
      <Skeleton className="h-4 w-[20px]" />
      <Skeleton className="h-4 w-[100px]" />
      <Skeleton className="h-4 w-[100px]" />
      <Skeleton className="h-4 w-[80px]" />
      <Skeleton className="h-4 w-[80px]" />
      <Skeleton className="h-4 w-[80px]" />
    </div>
  );
}

// Table skeleton with header and multiple rows
interface TableSkeletonProps extends SkeletonProps {
  rowCount?: number;
}

function TableSkeleton({ className, rowCount = 5, ...props }: TableSkeletonProps) {
  return (
    <div className={cn("w-full", className)} {...props}>
      <div className="flex items-center space-x-4 py-3 border-b">
        <div className="h-4 w-[20px] font-medium">#</div>
        <div className="h-4 w-[100px] font-medium">Name</div>
        <div className="h-4 w-[100px] font-medium">Position</div>
        <div className="h-4 w-[80px] font-medium">Played</div>
        <div className="h-4 w-[80px] font-medium">Win Rate</div>
        <div className="h-4 w-[80px] font-medium">Rating</div>
      </div>
      <div className="space-y-2 mt-2">
        {Array(rowCount)
          .fill(null)
          .map((_, index) => (
            <TableRowSkeleton key={index} />
          ))}
      </div>
    </div>
  );
}

// Stats card skeleton
function StatCardSkeleton({ className, ...props }: SkeletonProps) {
  return (
    <Card className={cn("overflow-hidden", className)} {...props}>
      <div className="p-4">
        <div className="flex justify-between items-center mb-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-4 rounded-full" />
        </div>
        <Skeleton className="h-8 w-16 mt-2" />
        <Skeleton className="h-3 w-20 mt-2" />
      </div>
    </Card>
  );
}

// Dashboard skeleton with stats and tables
function DashboardSkeleton({ className, ...props }: SkeletonProps) {
  return (
    <div className={cn("space-y-6", className)} {...props}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCardSkeleton />
        <StatCardSkeleton />
        <StatCardSkeleton />
        <StatCardSkeleton />
      </div>

      <CardSkeleton className="w-full" />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="overflow-hidden">
          <div className="p-6">
            <Skeleton className="h-6 w-1/3 mb-4" />
            <TableSkeleton rowCount={3} />
          </div>
        </Card>

        <Card className="overflow-hidden">
          <div className="p-6">
            <Skeleton className="h-6 w-1/3 mb-4" />
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Skeleton className="h-4 w-[120px]" />
                <Skeleton className="h-4 w-[80px]" />
              </div>
              <div className="flex justify-between items-center">
                <Skeleton className="h-4 w-[140px]" />
                <Skeleton className="h-4 w-[80px]" />
              </div>
              <div className="flex justify-between items-center">
                <Skeleton className="h-4 w-[100px]" />
                <Skeleton className="h-4 w-[80px]" />
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}

// Player profile skeleton
function PlayerProfileSkeleton({ className, ...props }: SkeletonProps) {
  return (
    <div className={cn("space-y-6", className)} {...props}>
      <Card className="overflow-hidden">
        <div className="p-6">
          <div className="flex items-center gap-4 mb-6">
            <Skeleton className="h-16 w-16 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-6 w-[150px]" />
              <Skeleton className="h-4 w-[100px]" />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Skeleton className="h-5 w-[100px]" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-5 w-[100px]" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-5 w-[100px]" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <CardSkeleton />
        <CardSkeleton />
      </div>
    </div>
  );
}

// Leaderboard skeleton
function LeaderboardSkeleton({ className, ...props }: SkeletonProps) {
  return (
    <Card className={cn("overflow-hidden", className)} {...props}>
      <div className="p-6">
        <Skeleton className="h-6 w-1/4 mb-6" />
        <TableSkeleton rowCount={10} />
      </div>
    </Card>
  );
}

// Form skeleton
function FormSkeleton({ className, ...props }: SkeletonProps) {
  return (
    <Card className={cn("overflow-hidden", className)} {...props}>
      <div className="p-6">
        <Skeleton className="h-6 w-1/3 mb-6" />

        <div className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-10 w-full" />
          </div>

          <div className="space-y-2">
            <Skeleton className="h-4 w-[120px]" />
            <Skeleton className="h-10 w-full" />
          </div>

          <div className="space-y-2">
            <Skeleton className="h-4 w-[80px]" />
            <Skeleton className="h-10 w-full" />
          </div>

          <div className="space-y-2">
            <Skeleton className="h-4 w-[150px]" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </div>
      </div>

      <div className="px-6 py-4 bg-muted/30 flex justify-end gap-2">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-24" />
      </div>
    </Card>
  );
}

// Loading showcase component
function SkeletonShowcase({ className, ...props }: SkeletonProps) {
  return (
    <div className={cn("container mx-auto p-6 space-y-10", className)} {...props}>
      <div>
        <h2 className="text-2xl font-bold mb-6">Card Skeletons</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <CardSkeleton />
          <PlayerCardSkeleton />
          <StatCardSkeleton />
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-6">Table Skeleton</h2>
        <Card className="overflow-hidden">
          <div className="p-6">
            <TableSkeleton rowCount={5} />
          </div>
        </Card>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-6">Dashboard Skeleton</h2>
        <DashboardSkeleton />
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-6">Form Skeleton</h2>
        <FormSkeleton />
      </div>
    </div>
  );
}

export {
  Skeleton,
  CardSkeleton,
  PlayerCardSkeleton,
  TableRowSkeleton,
  TableSkeleton,
  StatCardSkeleton,
  DashboardSkeleton,
  PlayerProfileSkeleton,
  LeaderboardSkeleton,
  FormSkeleton,
  SkeletonShowcase
}
