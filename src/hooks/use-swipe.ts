import { useState, useCallback } from 'react';

interface SwipeHandlers {
  onSwipedLeft?: () => void;
  onSwipedRight?: () => void;
}

export function useSwipe({ onSwipedLeft, onSwipedRight }: SwipeHandlers) {
  const [touchStart, setTouchStart] = useState<number | null>(null);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
  }, []);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!touchStart) return;

    const currentTouch = e.touches[0].clientX;
    const diff = touchStart - currentTouch;

    if (Math.abs(diff) > 50) {
      if (diff > 0 && onSwipedLeft) onSwipedLeft();
      if (diff < 0 && onSwipedRight) onSwipedRight();
      setTouchStart(null);
    }
  }, [touchStart, onSwipedLeft, onSwipedRight]);

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
  };
}
