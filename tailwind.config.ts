
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				// Soccer app specific colors - Updated to user preferences
				soccer: {
					primary: "#35db71", // User preferred primary color
					"primary-dark": "#2bb85e", // Slightly darker for dark mode
					"primary-light": "#4de085", // Lighter variant for hover states
					secondary: "#2bb85e", // Secondary shade
					accent: "#22a04a", // Darker accent
					light: "#f4f2ed", // User preferred light background
					dark: "#0f0f0f", // User preferred dark background
					"dark-100": "#0f0f0f", // Base dark color
					"dark-200": "#161616", // Slightly lighter for cards
					"dark-300": "#1c1c1c", // For elevated components
					"dark-400": "#242424", // For inputs and form elements
					"dark-500": "#2a2a2a", // For hover states
					"dark-600": "#333333", // For borders and dividers
					// Additional shades for better design system
					"primary-50": "#f0fdf4",
					"primary-100": "#dcfce7",
					"primary-200": "#bbf7d0",
					"primary-300": "#86efac",
					"primary-400": "#4ade80",
					"primary-500": "#35db71", // Main primary
					"primary-600": "#2bb85e",
					"primary-700": "#22a04a",
					"primary-800": "#1a7a37",
					"primary-900": "#166534",
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fade-in': {
					"0%": {
						opacity: "0",
						transform: "translateY(10px)"
					},
					"100%": {
						opacity: "1",
						transform: "translateY(0)"
					}
				},
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.3s ease-out',
				'pulse-focus': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
			},
			// Accessibility focus styles
			boxShadow: {
				'focus-visible': '0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--primary))',
				'focus-visible-dark': '0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--primary))',
				'focus-visible-destructive': '0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--destructive))',
				'focus-visible-accent': '0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--accent))',
				'focus-visible-muted': '0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--muted-foreground))',
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
